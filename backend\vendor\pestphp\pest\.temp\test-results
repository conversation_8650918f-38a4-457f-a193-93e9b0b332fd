{"version": "pest_3.8.1", "defects": {"Tests\\Feature\\CategoryApiTest::test_can_get_all_categories": 7, "Tests\\Feature\\ProductApiTest::test_can_get_all_products": 7, "Tests\\Feature\\ProductApiTest::test_can_delete_product": 7, "Tests\\Feature\\UserApiTest::test_user_can_update_password": 8}, "times": {"Tests\\Feature\\CategoryApiTest::test_can_get_all_categories": 0.127, "Tests\\Feature\\CategoryApiTest::test_can_create_category": 0.029, "Tests\\Feature\\CategoryApiTest::test_can_update_category": 0.016, "Tests\\Feature\\CategoryApiTest::test_can_delete_category": 0.015, "Tests\\Feature\\ProductApiTest::test_can_get_all_products": 0.022, "Tests\\Feature\\ProductApiTest::test_can_create_product": 0.022, "Tests\\Feature\\ProductApiTest::test_can_update_product": 0.015, "Tests\\Feature\\ProductApiTest::test_can_delete_product": 0.012, "Tests\\Feature\\ProductApiTest::test_can_search_products": 0.014, "P\\Tests\\Unit\\ExampleTest::__pest_evaluable_that_true_is_true": 0.018, "P\\Tests\\Feature\\ExampleTest::__pest_evaluable_it_returns_a_successful_response": 0.032, "Tests\\Feature\\ProductApiTest::test_can_update_stocks": 0.021, "Tests\\Feature\\UserApiTest::test_user_can_update_their_profile": 10.724, "Tests\\Feature\\UserApiTest::test_user_can_update_password": 0.825}}