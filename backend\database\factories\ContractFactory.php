<?php

namespace Database\Factories;

use App\Models\Contract;
use App\Models\User;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Contract>
 */
class ContractFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-1 year', '+6 months');
        $endDate = $this->faker->dateTimeBetween($startDate, '+2 years');

        return [
            'user_id' => User::factory(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'monthly_salary' => $this->faker->numberBetween(30000, 100000),
            'shift_id' => Shift::factory(),
            'pharmacy_id' => null, // Will be set when pharmacies are implemented
        ];
    }

    /**
     * Create an active contract.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => now()->subMonths(3),
            'end_date' => now()->addMonths(9),
        ]);
    }

    /**
     * Create an expired contract.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => now()->subYear(),
            'end_date' => now()->subMonths(3),
        ]);
    }

    /**
     * Create a future contract.
     */
    public function future(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => now()->addMonths(2),
            'end_date' => now()->addYear(),
        ]);
    }

    /**
     * Create a contract ending soon.
     */
    public function endingSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => now()->subMonths(6),
            'end_date' => now()->addDays(15),
        ]);
    }

    /**
     * Create a high salary contract.
     */
    public function highSalary(): static
    {
        return $this->state(fn (array $attributes) => [
            'monthly_salary' => $this->faker->numberBetween(80000, 150000),
        ]);
    }

    /**
     * Create a contract for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create a contract for a specific shift.
     */
    public function forShift(Shift $shift): static
    {
        return $this->state(fn (array $attributes) => [
            'shift_id' => $shift->id,
        ]);
    }
}
