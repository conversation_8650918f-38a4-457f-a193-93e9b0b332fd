<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Pharmacy extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        // Will be filled when pharmacies are implemented
    ];

    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the contracts for the pharmacy.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }
}
