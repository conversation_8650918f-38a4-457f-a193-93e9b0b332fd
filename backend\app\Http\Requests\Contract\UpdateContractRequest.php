<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class UpdateContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userId' => 'sometimes|required|uuid|exists:users,id',
            'startDate' => 'sometimes|required|date',
            'endDate' => 'sometimes|required|date|after:startDate',
            'monthlySalary' => 'sometimes|required|integer|min:1',
            'shiftId' => 'sometimes|required|uuid|exists:shifts,id',
            'pharmacyId' => 'nullable|uuid', // Will add exists validation when pharmacies table is created
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'userId.required' => 'User ID is required.',
            'userId.uuid' => 'User ID must be a valid UUID.',
            'userId.exists' => 'The selected user does not exist.',
            'startDate.required' => 'Start date is required.',
            'startDate.date' => 'Start date must be a valid date.',
            'endDate.required' => 'End date is required.',
            'endDate.date' => 'End date must be a valid date.',
            'endDate.after' => 'End date must be after start date.',
            'monthlySalary.required' => 'Monthly salary is required.',
            'monthlySalary.integer' => 'Monthly salary must be a number.',
            'monthlySalary.min' => 'Monthly salary must be at least 1.',
            'shiftId.required' => 'Shift ID is required.',
            'shiftId.uuid' => 'Shift ID must be a valid UUID.',
            'shiftId.exists' => 'The selected shift does not exist.',
            'pharmacyId.uuid' => 'Pharmacy ID must be a valid UUID.',
        ];
    }

    /**
     * Get the validated data with proper field mapping.
     */
    public function validatedData(): array
    {
        $validated = $this->validated();

        $data = [];
        foreach ($validated as $key => $value) {
            $dbKey = match($key) {
                'userId' => 'user_id',
                'startDate' => 'start_date',
                'endDate' => 'end_date',
                'monthlySalary' => 'monthly_salary',
                'shiftId' => 'shift_id',
                'pharmacyId' => 'pharmacy_id',
                default => $key
            };
            $data[$dbKey] = $value;
        }

        return $data;
    }
}
