<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class EndContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'endDate' => 'nullable|date|after_or_equal:today',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'endDate.date' => 'End date must be a valid date.',
            'endDate.after_or_equal' => 'End date must be today or in the future.',
        ];
    }

    /**
     * Get the validated data with proper field mapping.
     */
    public function validatedData(): array
    {
        $validated = $this->validated();

        return [
            'end_date' => $validated['endDate'] ?? null,
        ];
    }
}
