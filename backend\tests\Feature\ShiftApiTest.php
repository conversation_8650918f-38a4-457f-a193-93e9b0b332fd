<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Shift;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShiftApiTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    public function test_can_create_shift()
    {
        $shiftData = [
            'start' => '09:00',
            'end' => '17:00',
            'daysOfWeek' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ];

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/shifts', $shiftData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'start',
                    'end',
                    'daysOfWeek',
                    'createdAt',
                    'updatedAt',
                    'contractsCount'
                ],
                'message'
            ]);

        $this->assertDatabaseHas('shifts', [
            'start' => '09:00',
            'end' => '17:00',
        ]);
    }

    public function test_can_get_shifts()
    {
        Shift::factory()->create([
            'start' => '09:00',
            'end' => '17:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/shifts');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'start',
                        'end',
                        'daysOfWeek',
                        'createdAt',
                        'updatedAt',
                        'contractsCount'
                    ]
                ]
            ]);
    }

    public function test_can_get_single_shift()
    {
        $shift = Shift::factory()->create([
            'start' => '09:00',
            'end' => '17:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/shifts/{$shift->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'start',
                    'end',
                    'daysOfWeek',
                    'createdAt',
                    'updatedAt',
                    'contractsCount'
                ]
            ]);
    }

    public function test_can_update_shift()
    {
        $shift = Shift::factory()->create([
            'start' => '09:00',
            'end' => '17:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $updateData = [
            'start' => '08:00',
            'end' => '16:00',
            'daysOfWeek' => ['monday', 'wednesday', 'friday']
        ];

        $response = $this->actingAs($this->user, 'api')
            ->putJson("/api/shifts/{$shift->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'start',
                    'end',
                    'daysOfWeek',
                    'createdAt',
                    'updatedAt',
                    'contractsCount'
                ],
                'message'
            ]);

        $this->assertDatabaseHas('shifts', [
            'id' => $shift->id,
            'start' => '08:00',
            'end' => '16:00',
        ]);
    }

    public function test_can_delete_shift()
    {
        $shift = Shift::factory()->create([
            'start' => '09:00:00',
            'end' => '17:00:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]);

        $response = $this->actingAs($this->user, 'api')
            ->deleteJson("/api/shifts/{$shift->id}");

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Shift deleted successfully'
            ]);

        $this->assertSoftDeleted('shifts', [
            'id' => $shift->id
        ]);
    }

    public function test_validation_errors_on_create_shift()
    {
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/shifts', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start', 'end', 'daysOfWeek']);
    }

    public function test_end_time_must_be_after_start_time()
    {
        $shiftData = [
            'start' => '17:00',
            'end' => '09:00',
            'daysOfWeek' => ['monday']
        ];

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/shifts', $shiftData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end']);
    }
}
