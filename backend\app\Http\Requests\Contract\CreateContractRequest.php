<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class CreateContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'userId' => 'required|uuid|exists:users,id',
            'startDate' => 'required|date|after_or_equal:today',
            'endDate' => 'required|date|after:startDate',
            'monthlySalary' => 'required|integer|min:1',
            'shiftId' => 'required|uuid|exists:shifts,id',
            'pharmacyId' => 'nullable|uuid', // Will add exists validation when pharmacies table is created
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'userId.required' => 'User ID is required.',
            'userId.uuid' => 'User ID must be a valid UUID.',
            'userId.exists' => 'The selected user does not exist.',
            'startDate.required' => 'Start date is required.',
            'startDate.date' => 'Start date must be a valid date.',
            'startDate.after_or_equal' => 'Start date must be today or in the future.',
            'endDate.required' => 'End date is required.',
            'endDate.date' => 'End date must be a valid date.',
            'endDate.after' => 'End date must be after start date.',
            'monthlySalary.required' => 'Monthly salary is required.',
            'monthlySalary.integer' => 'Monthly salary must be a number.',
            'monthlySalary.min' => 'Monthly salary must be at least 1.',
            'shiftId.required' => 'Shift ID is required.',
            'shiftId.uuid' => 'Shift ID must be a valid UUID.',
            'shiftId.exists' => 'The selected shift does not exist.',
            'pharmacyId.uuid' => 'Pharmacy ID must be a valid UUID.',
        ];
    }

    /**
     * Get the validated data with proper field mapping.
     */
    public function validatedData(): array
    {
        $validated = $this->validated();

        return [
            'user_id' => $validated['userId'],
            'start_date' => $validated['startDate'],
            'end_date' => $validated['endDate'],
            'monthly_salary' => $validated['monthlySalary'],
            'shift_id' => $validated['shiftId'],
            'pharmacy_id' => $validated['pharmacyId'] ?? null,
        ];
    }
}
