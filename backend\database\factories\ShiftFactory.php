<?php

namespace Database\Factories;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shift>
 */
class ShiftFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startHour = $this->faker->numberBetween(6, 14);
        $endHour = $startHour + $this->faker->numberBetween(6, 10);
        
        return [
            'start' => sprintf('%02d:00:00', $startHour),
            'end' => sprintf('%02d:00:00', min($endHour, 23)),
            'days_of_week' => $this->faker->randomElements([
                'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
            ], $this->faker->numberBetween(1, 7)),
        ];
    }

    /**
     * Create a morning shift.
     */
    public function morning(): static
    {
        return $this->state(fn (array $attributes) => [
            'start' => '08:00:00',
            'end' => '16:00:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);
    }

    /**
     * Create an evening shift.
     */
    public function evening(): static
    {
        return $this->state(fn (array $attributes) => [
            'start' => '16:00:00',
            'end' => '00:00:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);
    }

    /**
     * Create a weekend shift.
     */
    public function weekend(): static
    {
        return $this->state(fn (array $attributes) => [
            'start' => '10:00:00',
            'end' => '18:00:00',
            'days_of_week' => ['saturday', 'sunday'],
        ]);
    }

    /**
     * Create a full-time shift.
     */
    public function fullTime(): static
    {
        return $this->state(fn (array $attributes) => [
            'start' => '09:00:00',
            'end' => '17:00:00',
            'days_of_week' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);
    }
}
