<?php

namespace App\Http\Requests\Shift;

use Illuminate\Foundation\Http\FormRequest;

class CreateShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'start' => 'required|date_format:H:i',
            'end' => 'required|date_format:H:i|after:start',
            'daysOfWeek' => 'required|array|min:1',
            'daysOfWeek.*' => 'required|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'start.required' => 'Start time is required.',
            'start.date_format' => 'Start time must be in HH:MM format.',
            'end.required' => 'End time is required.',
            'end.date_format' => 'End time must be in HH:MM format.',
            'end.after' => 'End time must be after start time.',
            'daysOfWeek.required' => 'At least one day of the week is required.',
            'daysOfWeek.array' => 'Days of week must be an array.',
            'daysOfWeek.min' => 'At least one day of the week must be selected.',
            'daysOfWeek.*.required' => 'Each day of the week is required.',
            'daysOfWeek.*.string' => 'Each day of the week must be a string.',
            'daysOfWeek.*.in' => 'Each day of the week must be a valid day (monday, tuesday, wednesday, thursday, friday, saturday, sunday).',
        ];
    }

    /**
     * Get the validated data with proper field mapping.
     */
    public function validatedData(): array
    {
        $validated = $this->validated();

        return [
            'start' => $validated['start'],
            'end' => $validated['end'],
            'days_of_week' => $validated['daysOfWeek'],
        ];
    }
}
