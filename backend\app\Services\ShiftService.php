<?php

namespace App\Services;

use App\Models\Shift;

class ShiftService
{
    public function getAllShifts(int $page = 1, int $perPage = 15, array $filters = [])
    {
        $query = Shift::query();

        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('start', 'like', "%{$filters['search']}%")
                  ->orWhere('end', 'like', "%{$filters['search']}%");
            });
        }

        if (isset($filters['days']) && !empty($filters['days'])) {
            $query->byDays($filters['days']);
        }

        if (isset($filters['start_time'])) {
            $query->where('start', '>=', $filters['start_time']);
        }

        if (isset($filters['end_time'])) {
            $query->where('end', '<=', $filters['end_time']);
        }

        return $query->paginate(perPage: $perPage, page: $page);
    }

    public function getShiftById(string $id)
    {
        return Shift::find($id);
    }

    public function createShift(array $data)
    {
        return Shift::create($data);
    }

    public function updateShift(string $id, array $data)
    {
        $shift = $this->getShiftById($id);

        if (!$shift) {
            return null;
        }

        $shift->update($data);
        return $shift;
    }

    public function deleteShift(string $id)
    {
        $shift = $this->getShiftById($id);

        if (!$shift) {
            return false;
        }

        // Check if shift is being used by any contracts
        if ($shift->contracts()->exists()) {
            return false; // Cannot delete shift that's being used
        }

        return $shift->delete();
    }

    public function getShiftsByDays(array $days, int $page = 1, int $perPage = 15)
    {
        return Shift::byDays($days)->paginate(perPage: $perPage, page: $page);
    }

    public function getAvailableShifts(int $page = 1, int $perPage = 15)
    {
        // Get shifts that are not currently assigned to any active contracts
        $query = Shift::whereDoesntHave('contracts', function ($q) {
            $q->active();
        });

        return $query->paginate(perPage: $perPage, page: $page);
    }
}
