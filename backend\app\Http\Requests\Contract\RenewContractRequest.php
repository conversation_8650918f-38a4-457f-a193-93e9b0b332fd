<?php

namespace App\Http\Requests\Contract;

use Illuminate\Foundation\Http\FormRequest;

class RenewContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'endDate' => 'required|date|after:today',
            'monthlySalary' => 'nullable|integer|min:1',
            'shiftId' => 'nullable|uuid|exists:shifts,id',
            'pharmacyId' => 'nullable|uuid', // Will add exists validation when pharmacies table is created
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'endDate.required' => 'End date is required for contract renewal.',
            'endDate.date' => 'End date must be a valid date.',
            'endDate.after' => 'End date must be in the future.',
            'monthlySalary.integer' => 'Monthly salary must be a number.',
            'monthlySalary.min' => 'Monthly salary must be at least 1.',
            'shiftId.uuid' => 'Shift ID must be a valid UUID.',
            'shiftId.exists' => 'The selected shift does not exist.',
            'pharmacyId.uuid' => 'Pharmacy ID must be a valid UUID.',
        ];
    }

    /**
     * Get the validated data with proper field mapping.
     */
    public function validatedData(): array
    {
        $validated = $this->validated();

        $data = [];
        foreach ($validated as $key => $value) {
            $dbKey = match($key) {
                'endDate' => 'end_date',
                'monthlySalary' => 'monthly_salary',
                'shiftId' => 'shift_id',
                'pharmacyId' => 'pharmacy_id',
                default => $key
            };
            $data[$dbKey] = $value;
        }

        return $data;
    }
}
